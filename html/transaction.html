<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purro - Transaction Confirmation</title>
    <link rel="stylesheet" href="../styles/global.css">
    <link rel="stylesheet" href="../styles/popup.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 375px;
            height: 600px;
            overflow: hidden;
            background: var(--background-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .transaction-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .transaction-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            background: var(--card-color);
        }
        
        .transaction-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        
        .transaction-footer {
            padding: 16px;
            border-top: 1px solid var(--border-color);
            background: var(--card-color);
        }
        
        .site-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .site-favicon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: var(--background-color);
        }
        
        .site-details h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .site-details p {
            margin: 0;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .transaction-details {
            background: var(--background-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .detail-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
            text-align: right;
            max-width: 200px;
            word-break: break-all;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }
        
        .warning-text {
            font-size: 13px;
            color: #ffc107;
            margin: 0;
        }
        
        .button-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 16px;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-secondary {
            background: var(--background-color);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background: var(--card-color);
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-color-dark);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .message-content {
            background: var(--background-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            font-family: monospace;
            font-size: 13px;
            color: var(--text-primary);
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="transaction-container">
        <div class="transaction-header">
            <div class="site-info">
                <img id="siteFavicon" class="site-favicon" src="" alt="Site" style="display: none;">
                <div class="site-favicon" id="defaultFavicon">🌐</div>
                <div class="site-details">
                    <h3 id="siteTitle">Transaction Request</h3>
                    <p id="siteOrigin">Loading...</p>
                </div>
            </div>
        </div>
        
        <div class="transaction-content">
            <div id="transactionDetails" class="transaction-details">
                <!-- Transaction details will be populated here -->
            </div>
            
            <div class="warning-box">
                <p class="warning-text">
                    ⚠️ Only approve transactions you understand and trust. 
                    Malicious transactions can drain your wallet.
                </p>
            </div>
        </div>
        
        <div class="transaction-footer">
            <div class="button-group">
                <button id="rejectBtn" class="btn btn-secondary">
                    Cancel
                </button>
                <button id="approveBtn" class="btn btn-primary">
                    <span id="approveText">Confirm</span>
                    <div id="approveLoading" class="loading" style="display: none;">
                        <div class="spinner"></div>
                        Processing...
                    </div>
                </button>
            </div>
        </div>
    </div>

    <script src="../scripts/transaction.js"></script>
</body>
</html>
