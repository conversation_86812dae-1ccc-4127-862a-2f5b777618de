import { storage<PERSON>and<PERSON> } from './storage-handler';
import { supportedEVMChains } from '../constants/supported-chains';
import { STORAGE_KEYS } from '../constants/storage-keys';

export interface MessageResponse {
    success: boolean;
    data?: any;
    error?: string;
    code?: number; // Error code for provider errors
    requestId?: string;
}

interface PendingConnection {
    origin: string;
    tabId?: number;
    timestamp: number;
    resolve: (response: any) => void;
    reject: (error: any) => void;
}

interface PendingTransaction {
    origin: string;
    tabId?: number;
    timestamp: number;
    transactionData: any;
    type: 'send' | 'sign' | 'personal_sign' | 'eth_sign' | 'typed_data';
    resolve: (response: any) => void;
    reject: (error: any) => void;
}

let pendingConnections: Map<string, PendingConnection> = new Map();
let pendingTransactions: Map<string, PendingTransaction> = new Map();

export const evmHandler = {
    async handleEthRequestAccounts(sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        let origin = 'unknown';
        let favicon = '';
        let title = '';

        if (sender.tab?.url) {
            const url = new URL(sender.tab.url);
            origin = url.origin;
            favicon = sender.tab.favIconUrl || '';
            title = sender.tab.title || '';
        }


        try {
            // First check connection status using the unified method
            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);

            if (connectionStatus.success && connectionStatus.data?.isConnected) {


                // Close any existing connect popups since we have existing connection
                await this.closeExistingConnectPopups();

                return {
                    success: true,
                    data: {
                        accounts: connectionStatus.data.accounts,
                        activeAccount: connectionStatus.data.activeAccount
                    }
                };
            }



            // Validate wallet state before showing popup
            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No EVM wallet found for active account');
            }

            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                throw new Error('No active account found');
            }

            const wallet = await storageHandler.getWalletById(activeAccount.id);
            if (!wallet || !wallet.eip155) {
                throw new Error('EVM wallet not found for this account');
            }

            // Create pending connection and show popup
            const connectionPromise = createPendingConnection(origin, sender.tab?.id);

            const connectUrl = chrome.runtime.getURL('html/connect.html') +
                `?origin=${encodeURIComponent(origin)}&favicon=${encodeURIComponent(favicon)}&title=${encodeURIComponent(title)}`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: connectUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const connectionResult = await connectionPromise;

            return {
                success: true,
                data: connectionResult,
            };
        } catch (error) {
            console.error('Error in handleEthRequestAccounts:', error);
            throw error;
        }
    },

    async handleApproveConnection(data: { origin: string; accountId: string; favicon?: string }): Promise<MessageResponse> {
        try {
            const account = await storageHandler.getAccountById(data.accountId);

            if (!account) {
                return {
                    success: false,
                    error: 'Account not found',
                    code: 4001
                };
            }

            // Get the wallet for this account to get the address
            const wallet = await storageHandler.getWalletById(data.accountId);
            if (!wallet || !wallet.eip155) {
                return {
                    success: false,
                    error: 'EVM wallet not found for this account',
                    code: 4001
                };
            }

            // Save the connected site
            await storageHandler.saveConnectedSite(data.accountId, {
                origin: data.origin,
                favicon: data.favicon,
                timestamp: Date.now()
            });

            // Resolve the pending connection with the account address
            const connectionResult = {
                accounts: [wallet.eip155.address],
                activeAccount: wallet.eip155.address
            };


            const pendingConnection = Array.from(pendingConnections.values())
                .find(conn => conn.origin === data.origin);

            if (pendingConnection) {

                pendingConnection.resolve(connectionResult);
                // Remove from pending connections
                for (const [key, conn] of pendingConnections.entries()) {
                    if (conn.origin === data.origin) {
                        pendingConnections.delete(key);

                        break;
                    }
                }
            } else {
                console.warn('⚠️ No pending connection found for origin:', data.origin);
            }

            return {
                success: true,
                data: connectionResult
            };
        } catch (error) {
            console.error('Error in handleApproveConnection:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to approve connection',
                code: 4001
            };
        }
    },

    async handleRejectConnection(data: { origin: string }): Promise<MessageResponse> {
        try {
            // Find and reject the pending connection
            const pendingConnection = Array.from(pendingConnections.values())
                .find(conn => conn.origin === data.origin);

            if (pendingConnection) {
                pendingConnection.reject(new Error('User rejected the request'));
                // Remove from pending connections
                for (const [key, conn] of pendingConnections.entries()) {
                    if (conn.origin === data.origin) {
                        pendingConnections.delete(key);
                        break;
                    }
                }
            }

            return {
                success: true,
                data: { rejected: true }
            };
        } catch (error) {
            console.error('Error in handleRejectConnection:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to reject connection',
                code: 4001
            };
        }
    },

    async handleGetCurrentChainId(): Promise<MessageResponse> {
        try {
            // Get current chain ID from storage, default to Ethereum mainnet
            const result = await chrome.storage.local.get(STORAGE_KEYS.CURRENT_CHAIN_ID);
            const chainId = result[STORAGE_KEYS.CURRENT_CHAIN_ID] || '0x1'; // Default to Ethereum mainnet

            return {
                success: true,
                data: { chainId: parseInt(chainId, 16) } // Return as number for compatibility
            };
        } catch (error) {
            console.error('Error in handleGetCurrentChainId:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get current chain ID',
                code: 4001
            };
        }
    },

    async handleSwitchEthereumChain(data: { chainId: string }): Promise<MessageResponse> {
        try {
            const { chainId } = data;

            // Validate chain ID format (should be hex string like "0x1")
            if (!chainId || !chainId.startsWith('0x')) {
                return {
                    success: false,
                    error: 'Invalid chain ID format. Expected hex string like "0x1"',
                    code: 4902 // Unrecognized chain ID
                };
            }

            // Check if the chain is supported
            if (!supportedEVMChains[chainId]) {
                return {
                    success: false,
                    error: `Unsupported chain ID: ${chainId}. Supported chains: ${Object.keys(supportedEVMChains).join(', ')}`,
                    code: 4902 // Unrecognized chain ID
                };
            }

            // Save the new chain ID
            await chrome.storage.local.set({
                [STORAGE_KEYS.CURRENT_CHAIN_ID]: chainId
            });

            return {
                success: true,
                data: { chainId: parseInt(chainId, 16) }
            };
        } catch (error) {
            console.error('Error in handleSwitchEthereumChain:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to switch chain',
                code: 4001
            };
        }
    },

    async handleCheckConnectionStatus(data: { origin: string }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = data.origin;

            // If origin not provided in data, get from sender
            if (!origin && sender.tab?.url) {
                const url = new URL(sender.tab.url);
                origin = url.origin;
            }

            if (!origin) {
                return {
                    success: false,
                    error: 'Origin not found',
                    code: 4001
                };
            }



            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                return {
                    success: true,
                    data: { isConnected: false, accounts: [] }
                };
            }

            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                return {
                    success: true,
                    data: { isConnected: false, accounts: [] }
                };
            }

            const [wallet, connectedSites] = await Promise.all([
                storageHandler.getWalletById(activeAccount.id),
                storageHandler.getConnectedSites(activeAccount.id)
            ]);

            const existingConnection = connectedSites.find(site => site.origin === origin);

            if (existingConnection && wallet && wallet.eip155) {

                return {
                    success: true,
                    data: {
                        isConnected: true,
                        accounts: [wallet.eip155.address],
                        activeAccount: wallet.eip155.address
                    }
                };
            }


            return {
                success: true,
                data: { isConnected: false, accounts: [] }
            };
        } catch (error) {
            console.error('Error in handleCheckConnectionStatus:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to check connection status',
                code: 4001
            };
        }
    },

    async handleConnectWallet(sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            // CONNECT_WALLET is essentially the same as ETH_REQUEST_ACCOUNTS
            // Delegate to the existing handler
            return await this.handleEthRequestAccounts(sender);
        } catch (error) {
            console.error('Error in handleConnectWallet:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to connect wallet',
                code: 4001
            };
        }
    },

    async handleDisconnectWallet(data: { origin?: string }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = data?.origin;

            // If origin not provided in data, get from sender
            if (!origin && sender.tab?.url) {
                const url = new URL(sender.tab.url);
                origin = url.origin;
            }

            if (!origin) {
                return {
                    success: false,
                    error: 'Origin not found',
                    code: 4001
                };
            }


            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                return {
                    success: true,
                    data: { disconnected: true }
                };
            }

            // Remove the connected site
            await storageHandler.deleteConnectedSite(activeAccount.id, origin);


            return {
                success: true,
                data: { disconnected: true }
            };
        } catch (error) {
            console.error('Error in handleDisconnectWallet:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to disconnect wallet',
                code: 4001
            };
        }
    },

    // Helper methods
    async closeExistingConnectPopups(): Promise<void> {
        try {
            const windows = await chrome.windows.getAll({ populate: true });
            for (const window of windows) {
                if (window.type === 'popup' && window.tabs) {
                    for (const tab of window.tabs) {
                        if (tab.url && tab.url.includes('connect.html')) {

                            if (window.id) {
                                await chrome.windows.remove(window.id);
                            }
                            break;
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to close existing popups:', error);
        }
    },

    async calculatePopupPosition(sender: chrome.runtime.MessageSender): Promise<{
        width: number;
        height: number;
        left: number;
        top: number;
    }> {
        const popupWidth = 375;
        const popupHeight = 600;
        let left = 100;
        let top = 100;

        if (sender?.tab?.id) {
            try {
                const currentTab = await chrome.tabs.get(sender.tab.id);
                const window = await chrome.windows.get(currentTab.windowId);

                // Calculate position relative to the current tab
                left = (window.left || 0) + (window.width || 0) - popupWidth;
                top = window.top || 0;
            } catch (error) {
                // Use default position if tab info is not available
                console.warn('Could not calculate popup position, using defaults:', error);
            }
        }

        return {
            width: popupWidth,
            height: popupHeight,
            left,
            top
        };
    },

    // Transaction signing handlers
    async handleEvmSendTransaction(data: { transactionData: any }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = 'unknown';
            let favicon = '';
            let title = '';

            if (sender.tab?.url) {
                const url = new URL(sender.tab.url);
                origin = url.origin;
                favicon = sender.tab.favIconUrl || '';
                title = sender.tab.title || '';
            }

            console.log('🔄 Processing send transaction request from:', origin);

            // Validate wallet state
            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No wallet found');
            }

            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                throw new Error('No active account found');
            }

            // Check if site is connected
            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);
            if (!connectionStatus.success || !connectionStatus.data?.isConnected) {
                throw new Error('Site not connected to wallet');
            }

            // Create pending transaction and show popup
            const transactionPromise = createPendingTransaction(origin, data.transactionData, 'send', sender.tab?.id);

            const transactionUrl = chrome.runtime.getURL('html/transaction.html') +
                `?origin=${encodeURIComponent(origin)}&favicon=${encodeURIComponent(favicon)}&title=${encodeURIComponent(title)}&type=send`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: transactionUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const transactionResult = await transactionPromise;

            return {
                success: true,
                data: transactionResult,
            };
        } catch (error) {
            console.error('Error in handleEvmSendTransaction:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to send transaction',
                code: 4001
            };
        }
    },

    async handleEvmSignTransaction(data: { transactionData: any }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = 'unknown';
            let favicon = '';
            let title = '';

            if (sender.tab?.url) {
                const url = new URL(sender.tab.url);
                origin = url.origin;
                favicon = sender.tab.favIconUrl || '';
                title = sender.tab.title || '';
            }

            console.log('🔄 Processing sign transaction request from:', origin);

            // Similar validation as send transaction
            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No wallet found');
            }

            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                throw new Error('No active account found');
            }

            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);
            if (!connectionStatus.success || !connectionStatus.data?.isConnected) {
                throw new Error('Site not connected to wallet');
            }

            const transactionPromise = createPendingTransaction(origin, data.transactionData, 'sign', sender.tab?.id);

            const transactionUrl = chrome.runtime.getURL('html/transaction.html') +
                `?origin=${encodeURIComponent(origin)}&favicon=${encodeURIComponent(favicon)}&title=${encodeURIComponent(title)}&type=sign`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: transactionUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const transactionResult = await transactionPromise;

            return {
                success: true,
                data: transactionResult,
            };
        } catch (error) {
            console.error('Error in handleEvmSignTransaction:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to sign transaction',
                code: 4001
            };
        }
    },

    async handlePersonalSign(data: { message: string; address?: string }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = 'unknown';
            let favicon = '';
            let title = '';

            if (sender.tab?.url) {
                const url = new URL(sender.tab.url);
                origin = url.origin;
                favicon = sender.tab.favIconUrl || '';
                title = sender.tab.title || '';
            }

            console.log('🔄 Processing personal sign request from:', origin);

            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No wallet found');
            }

            const activeAccount = await storageHandler.getActiveAccount();
            if (!activeAccount) {
                throw new Error('No active account found');
            }

            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);
            if (!connectionStatus.success || !connectionStatus.data?.isConnected) {
                throw new Error('Site not connected to wallet');
            }

            const signData = {
                message: data.message,
                address: data.address || connectionStatus.data.activeAccount
            };

            const transactionPromise = createPendingTransaction(origin, signData, 'personal_sign', sender.tab?.id);

            const transactionUrl = chrome.runtime.getURL('html/transaction.html') +
                `?origin=${encodeURIComponent(origin)}&favicon=${encodeURIComponent(favicon)}&title=${encodeURIComponent(title)}&type=personal_sign`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: transactionUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const transactionResult = await transactionPromise;

            return {
                success: true,
                data: transactionResult,
            };
        } catch (error) {
            console.error('Error in handlePersonalSign:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to sign message',
                code: 4001
            };
        }
    },

    async handleEthSign(data: { address: string; message: string }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = 'unknown';
            if (sender.tab?.url) {
                origin = new URL(sender.tab.url).origin;
            }

            console.log('🔄 Processing eth_sign request from:', origin);

            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No wallet found');
            }

            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);
            if (!connectionStatus.success || !connectionStatus.data?.isConnected) {
                throw new Error('Site not connected to wallet');
            }

            const signData = {
                address: data.address || connectionStatus.data.activeAccount,
                message: data.message
            };

            const transactionPromise = createPendingTransaction(origin, signData, 'eth_sign', sender.tab?.id);

            const transactionUrl = chrome.runtime.getURL('html/transaction.html') +
                `?origin=${encodeURIComponent(origin)}&type=eth_sign`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: transactionUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const transactionResult = await transactionPromise;

            return {
                success: true,
                data: transactionResult,
            };
        } catch (error) {
            console.error('Error in handleEthSign:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to sign message',
                code: 4001
            };
        }
    },

    async handleSignTypedData(data: { address: string; typedData: any }, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
        try {
            let origin = 'unknown';
            if (sender.tab?.url) {
                origin = new URL(sender.tab.url).origin;
            }

            console.log('🔄 Processing sign typed data request from:', origin);

            const { hasWallet } = await storageHandler.getWalletState();
            if (!hasWallet) {
                throw new Error('No wallet found');
            }

            const connectionStatus = await this.handleCheckConnectionStatus({ origin }, sender);
            if (!connectionStatus.success || !connectionStatus.data?.isConnected) {
                throw new Error('Site not connected to wallet');
            }

            const signData = {
                address: data.address || connectionStatus.data.activeAccount,
                typedData: data.typedData
            };

            const transactionPromise = createPendingTransaction(origin, signData, 'typed_data', sender.tab?.id);

            const transactionUrl = chrome.runtime.getURL('html/transaction.html') +
                `?origin=${encodeURIComponent(origin)}&type=typed_data`;

            const popupConfig = await this.calculatePopupPosition(sender);
            await chrome.windows.create({
                url: transactionUrl,
                type: 'popup',
                width: popupConfig.width,
                height: popupConfig.height,
                focused: true,
                left: popupConfig.left,
                top: popupConfig.top,
            });

            const transactionResult = await transactionPromise;

            return {
                success: true,
                data: transactionResult,
            };
        } catch (error) {
            console.error('Error in handleSignTypedData:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to sign typed data',
                code: 4001
            };
        }
    },

    async handleApproveTransaction(data: { transactionId: string; signature?: string; transactionHash?: string }): Promise<MessageResponse> {
        try {
            console.log('✅ Approving transaction:', data.transactionId);

            // Find the pending transaction
            const pendingTransaction = Array.from(pendingTransactions.values())
                .find(tx => `${tx.origin}_${tx.type}_${tx.timestamp}` === data.transactionId);

            if (!pendingTransaction) {
                console.warn('⚠️ No pending transaction found for ID:', data.transactionId);
                return {
                    success: false,
                    error: 'Transaction not found or expired',
                    code: 4001
                };
            }

            // Prepare result based on transaction type
            let result: any;
            if (pendingTransaction.type === 'send') {
                result = { transactionHash: data.transactionHash };
            } else {
                result = { signature: data.signature };
            }

            // Resolve the pending transaction
            pendingTransaction.resolve(result);

            // Remove from pending transactions
            for (const [key, tx] of pendingTransactions.entries()) {
                if (tx === pendingTransaction) {
                    pendingTransactions.delete(key);
                    break;
                }
            }

            return {
                success: true,
                data: result
            };
        } catch (error) {
            console.error('Error in handleApproveTransaction:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to approve transaction',
                code: 4001
            };
        }
    },

    async handleRejectTransaction(data: { transactionId: string }): Promise<MessageResponse> {
        try {
            console.log('❌ Rejecting transaction:', data.transactionId);

            // Find and reject the pending transaction
            const pendingTransaction = Array.from(pendingTransactions.values())
                .find(tx => `${tx.origin}_${tx.type}_${tx.timestamp}` === data.transactionId);

            if (pendingTransaction) {
                pendingTransaction.reject(new Error('User rejected the transaction'));

                // Remove from pending transactions
                for (const [key, tx] of pendingTransactions.entries()) {
                    if (tx === pendingTransaction) {
                        pendingTransactions.delete(key);
                        break;
                    }
                }
            }

            return {
                success: true,
                data: { rejected: true }
            };
        } catch (error) {
            console.error('Error in handleRejectTransaction:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to reject transaction',
                code: 4001
            };
        }
    }
}

function createPendingConnection(origin: string, tabId?: number): Promise<any> {
    return new Promise((resolve, reject) => {
        const connectionId = `${origin}_${Date.now()}`;

        pendingConnections.set(connectionId, {
            origin,
            tabId,
            timestamp: Date.now(),
            resolve,
            reject,
        });

        // Auto-cleanup after 1 minutes
        setTimeout(() => {
            if (pendingConnections.has(connectionId)) {
                pendingConnections.delete(connectionId);
                reject(new Error('Connection request timeout - Please try connecting again'));
            }
        }, 1 * 60 * 1000);
    });
}

function createPendingTransaction(origin: string, transactionData: any, type: 'send' | 'sign' | 'personal_sign' | 'eth_sign' | 'typed_data', tabId?: number): Promise<any> {
    return new Promise((resolve, reject) => {
        const transactionId = `${origin}_${type}_${Date.now()}`;

        console.log('🔄 Creating pending transaction:', transactionId);

        pendingTransactions.set(transactionId, {
            origin,
            tabId,
            timestamp: Date.now(),
            transactionData,
            type,
            resolve,
            reject,
        });

        // Auto-cleanup after 5 minutes (longer than connection)
        setTimeout(() => {
            if (pendingTransactions.has(transactionId)) {
                console.log('⏰ Transaction timeout:', transactionId);
                pendingTransactions.delete(transactionId);
                reject(new Error('Transaction request timeout - Please try again'));
            }
        }, 5 * 60 * 1000);
    });
}