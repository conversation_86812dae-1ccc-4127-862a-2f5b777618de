// Transaction confirmation popup script

let transactionRequest = null;
let transactionType = null;
let loading = false;

// Utility function to send messages to background script
const sendMessage = (type, data = {}) => {
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ type, data }, (response) => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
                return;
            }
            
            if (!response) {
                reject(new Error("No response received"));
                return;
            }
            
            if (response.success) {
                resolve(response.data);
            } else {
                reject(new Error(response.error || "Unknown error"));
            }
        });
    });
};

// Parse URL parameters
function getUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    return {
        origin: urlParams.get('origin') || 'unknown',
        favicon: urlParams.get('favicon') || '',
        title: urlParams.get('title') || 'Unknown Site',
        type: urlParams.get('type') || 'send'
    };
}

// Initialize the popup
async function init() {
    try {
        const params = getUrlParams();
        transactionType = params.type;
        
        console.log('🔄 Initializing transaction popup:', params);
        
        // Update site info
        document.getElementById('siteOrigin').textContent = params.origin;
        document.getElementById('siteTitle').textContent = params.title;
        
        if (params.favicon) {
            const favicon = document.getElementById('siteFavicon');
            favicon.src = params.favicon;
            favicon.style.display = 'block';
            document.getElementById('defaultFavicon').style.display = 'none';
            
            favicon.onerror = () => {
                favicon.style.display = 'none';
                document.getElementById('defaultFavicon').style.display = 'block';
            };
        }
        
        // Get pending transaction data
        await loadTransactionData(params.origin, params.type);
        
        // Setup event listeners
        setupEventListeners();
        
    } catch (error) {
        console.error('❌ Error initializing transaction popup:', error);
        showError('Failed to load transaction details');
    }
}

// Load transaction data from background script
async function loadTransactionData(origin, type) {
    try {
        // For now, we'll get the transaction data from the pending transactions
        // In a real implementation, you might want to pass transaction ID via URL params
        
        // Mock transaction data based on type
        const mockData = getMockTransactionData(type);
        transactionRequest = mockData;
        
        renderTransactionDetails(mockData, type);
        
    } catch (error) {
        console.error('❌ Error loading transaction data:', error);
        throw error;
    }
}

// Get mock transaction data for different types
function getMockTransactionData(type) {
    switch (type) {
        case 'send':
            return {
                to: '******************************************',
                value: '0x16345785d8a0000', // 0.1 ETH
                gas: '0x5208', // 21000
                gasPrice: '0x4a817c800', // 20 gwei
                data: '0x'
            };
        case 'sign':
            return {
                to: '******************************************',
                value: '0x16345785d8a0000',
                gas: '0x5208',
                gasPrice: '0x4a817c800',
                data: '0x'
            };
        case 'personal_sign':
            return {
                message: 'Hello, this is a test message for signing!',
                address: '******************************************'
            };
        case 'eth_sign':
            return {
                address: '******************************************',
                message: '0x48656c6c6f20576f726c64' // "Hello World" in hex
            };
        case 'typed_data':
            return {
                address: '******************************************',
                typedData: {
                    types: {
                        EIP712Domain: [
                            { name: 'name', type: 'string' },
                            { name: 'version', type: 'string' }
                        ],
                        Message: [
                            { name: 'content', type: 'string' }
                        ]
                    },
                    domain: {
                        name: 'Test App',
                        version: '1'
                    },
                    primaryType: 'Message',
                    message: {
                        content: 'Hello, World!'
                    }
                }
            };
        default:
            return {};
    }
}

// Render transaction details based on type
function renderTransactionDetails(data, type) {
    const container = document.getElementById('transactionDetails');
    container.innerHTML = '';
    
    switch (type) {
        case 'send':
        case 'sign':
            renderTransactionData(container, data);
            break;
        case 'personal_sign':
            renderPersonalSignData(container, data);
            break;
        case 'eth_sign':
            renderEthSignData(container, data);
            break;
        case 'typed_data':
            renderTypedDataSign(container, data);
            break;
    }
}

// Render transaction data (send/sign)
function renderTransactionData(container, data) {
    const details = [
        { label: 'To', value: data.to || 'N/A' },
        { label: 'Value', value: formatEthValue(data.value) },
        { label: 'Gas Limit', value: parseInt(data.gas || '0', 16).toLocaleString() },
        { label: 'Gas Price', value: formatGasPrice(data.gasPrice) },
        { label: 'Max Fee', value: calculateMaxFee(data.gas, data.gasPrice) }
    ];
    
    if (data.data && data.data !== '0x') {
        details.push({ label: 'Data', value: data.data });
    }
    
    details.forEach(detail => {
        const row = createDetailRow(detail.label, detail.value);
        container.appendChild(row);
    });
}

// Render personal sign data
function renderPersonalSignData(container, data) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message-content';
    messageDiv.textContent = data.message || 'No message';
    
    const label = document.createElement('div');
    label.className = 'detail-label';
    label.textContent = 'Message to sign:';
    label.style.marginBottom = '8px';
    
    container.appendChild(label);
    container.appendChild(messageDiv);
    
    if (data.address) {
        const addressRow = createDetailRow('Address', data.address);
        container.appendChild(addressRow);
    }
}

// Render eth_sign data
function renderEthSignData(container, data) {
    const details = [
        { label: 'Address', value: data.address || 'N/A' },
        { label: 'Message (hex)', value: data.message || 'N/A' }
    ];
    
    details.forEach(detail => {
        const row = createDetailRow(detail.label, detail.value);
        container.appendChild(row);
    });
}

// Render typed data sign
function renderTypedDataSign(container, data) {
    const typedDataDiv = document.createElement('div');
    typedDataDiv.className = 'message-content';
    typedDataDiv.textContent = JSON.stringify(data.typedData, null, 2);
    
    const label = document.createElement('div');
    label.className = 'detail-label';
    label.textContent = 'Typed data to sign:';
    label.style.marginBottom = '8px';
    
    container.appendChild(label);
    container.appendChild(typedDataDiv);
    
    if (data.address) {
        const addressRow = createDetailRow('Address', data.address);
        container.appendChild(addressRow);
    }
}

// Create a detail row element
function createDetailRow(label, value) {
    const row = document.createElement('div');
    row.className = 'detail-row';
    
    const labelDiv = document.createElement('div');
    labelDiv.className = 'detail-label';
    labelDiv.textContent = label;
    
    const valueDiv = document.createElement('div');
    valueDiv.className = 'detail-value';
    valueDiv.textContent = value;
    
    row.appendChild(labelDiv);
    row.appendChild(valueDiv);
    
    return row;
}

// Utility functions for formatting
function formatEthValue(value) {
    if (!value || value === '0x0') return '0 ETH';
    const wei = parseInt(value, 16);
    const eth = wei / 1e18;
    return `${eth.toFixed(6)} ETH`;
}

function formatGasPrice(gasPrice) {
    if (!gasPrice) return 'N/A';
    const wei = parseInt(gasPrice, 16);
    const gwei = wei / 1e9;
    return `${gwei.toFixed(2)} Gwei`;
}

function calculateMaxFee(gas, gasPrice) {
    if (!gas || !gasPrice) return 'N/A';
    const gasLimit = parseInt(gas, 16);
    const gasPriceWei = parseInt(gasPrice, 16);
    const maxFeeWei = gasLimit * gasPriceWei;
    const maxFeeEth = maxFeeWei / 1e18;
    return `${maxFeeEth.toFixed(6)} ETH`;
}

// Setup event listeners
function setupEventListeners() {
    document.getElementById('rejectBtn').addEventListener('click', handleReject);
    document.getElementById('approveBtn').addEventListener('click', handleApprove);
}

// Handle transaction approval
async function handleApprove() {
    if (loading) return;
    
    setLoading(true);
    
    try {
        console.log('✅ Approving transaction:', transactionType);
        
        // Mock transaction result
        const result = transactionType === 'send' 
            ? { transactionHash: '0x' + Math.random().toString(16).substr(2, 64) }
            : { signature: '0x' + Math.random().toString(16).substr(2, 130) };
        
        // In real implementation, this would call the actual signing/sending logic
        await sendMessage('APPROVE_TRANSACTION', {
            transactionId: `mock_${Date.now()}`,
            ...result
        });
        
        console.log('✅ Transaction approved successfully');
        
        // Close popup after short delay
        setTimeout(() => {
            window.close();
        }, 500);
        
    } catch (error) {
        console.error('❌ Error approving transaction:', error);
        showError('Failed to approve transaction: ' + error.message);
    } finally {
        setLoading(false);
    }
}

// Handle transaction rejection
async function handleReject() {
    if (loading) return;
    
    try {
        console.log('❌ Rejecting transaction');
        
        await sendMessage('REJECT_TRANSACTION', {
            transactionId: `mock_${Date.now()}`
        });
        
        console.log('❌ Transaction rejected');
        
        // Close popup
        window.close();
        
    } catch (error) {
        console.error('❌ Error rejecting transaction:', error);
        // Still close popup even if rejection fails
        window.close();
    }
}

// Set loading state
function setLoading(isLoading) {
    loading = isLoading;
    
    const approveBtn = document.getElementById('approveBtn');
    const approveText = document.getElementById('approveText');
    const approveLoading = document.getElementById('approveLoading');
    const rejectBtn = document.getElementById('rejectBtn');
    
    if (isLoading) {
        approveBtn.disabled = true;
        rejectBtn.disabled = true;
        approveText.style.display = 'none';
        approveLoading.style.display = 'flex';
    } else {
        approveBtn.disabled = false;
        rejectBtn.disabled = false;
        approveText.style.display = 'block';
        approveLoading.style.display = 'none';
    }
}

// Show error message
function showError(message) {
    // For now, just use alert. In production, you'd want a better error UI
    alert('Error: ' + message);
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', init);
